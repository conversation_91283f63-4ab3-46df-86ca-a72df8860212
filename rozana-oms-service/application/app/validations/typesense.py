import os
import logging
from typing import Dict, List, Tuple
from app.services.typesense_service import TypesenseService
from app.dto.orders import OrderItemCreate

logger = logging.getLogger(__name__)

PRICE_CHECK_ENABLED = os.getenv("PRICE_CHECK_ENABLED", "true").lower() == "true"

class TypesenseValidator:
    def __init__(self):
        self.typesense_service = TypesenseService()

    async def validate_wh_sku_and_pack_uom_quantity(self, product: Dict) -> bool:
        wh_sku = product.get("wh_sku")
        pack_uom_quantity = product.get("pack_uom_qty")
        facility_name = product.get("facility_code")
        sku = product.get("child_sku")

        if not wh_sku or not pack_uom_quantity:
            raise ValueError(f"wh_sku or pack_uom_qty not available for facility {facility_name} and sku {sku}")

    async def validate_product_price(self, product: Dict, payload_price: float) -> bool:
        # check if PRICE_CHECK_ENABLED is enabled
        if not PRICE_CHECK_ENABLED:
            return True

        typesense_price = product.get("selling_price") or product.get("price")
        facility_name = product.get("facility_code")
        sku = product.get("child_sku")

        if not typesense_price:
            raise ValueError(f"No price found in Typesense for SKU {sku} and facility {facility_name}")

        # validate price format
        try:
            typesense_price = float(typesense_price)
        except (ValueError, TypeError):
            raise ValueError(f"Invalid price format in Typesense for SKU {sku} and facility {facility_name}")

        # validate price value
        price_match = abs(payload_price - typesense_price) < 0.01
        if not price_match:
            raise ValueError(f"Price mismatch for SKU {sku}: payload={payload_price}, actual={typesense_price}")

        return price_match

    async def validate_items(self, items: List[OrderItemCreate], facility_name: str) -> Tuple[List[str], Dict[str, Dict]]:
        # here we have to collect all the errors at once and guve to the response not just first seen error
        errors = []
        products = {}
        for item in items:
            try:
                # Retrive the product from typesense
                product = await self.typesense_service.get_product_by_sku(item.sku, facility_name)
                if not product:
                    errors.append(f"Product {item.sku} Not Found for facility {facility_name}")
                    continue

                # Validate Each item
                await self.validate_wh_sku_and_pack_uom_quantity(product)
                await self.validate_product_price(product, item.sale_price)
                products[item.sku] = product
            except Exception as e:
                errors.append(str(e))
        return products, errors

    async def enrich_items(self, items: List[OrderItemCreate],  products: Dict[str, Dict], facility_name: str,) -> List[Dict]:
        enriched_items = []
        errors = []
        for item in items:
            try:
                product = products.get(item.sku)
                if not product:
                    raise ValueError(f"Product {item.sku} Not Found for facility {facility_name}")
                
                # Extract only required fields from product
                product_fields = self.typesense_service.extract_item_fields(product)
                enriched_item = {
                    "sku": item.sku,
                    "quantity": item.quantity,
                    "unit_price": item.unit_price,
                    "sale_price": item.sale_price,
                    **product_fields
                }
                enriched_items.append(enriched_item)
            except Exception as e:
                logger.error(f"Error enriching item {item.sku}: {str(e)}")
                errors.append(str(e))
        return enriched_items, errors

