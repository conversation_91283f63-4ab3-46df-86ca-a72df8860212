from typing import Dict
import random
import string
from datetime import datetime, timedelta
from sqlalchemy import text
from app.core.constants import OrderStatus
from app.connections.database import get_raw_transaction
import logging

logger = logging.getLogger(__name__)

def generate_random_prefix() -> str:
    """Generate a random 4-character alphanumeric string for order ID prefix.

    Returns:
        str: A 4-character string containing uppercase letters and digits (e.g., 'A2K4', '489K', 'X7B9')
    """
    characters = string.ascii_uppercase + string.digits
    return ''.join(random.choices(characters, k=4))


class OrderService:
    """Service for handling order commands (Create, Update, Cancel) using SQLAlchemy raw SQL"""

    def __init__(self):
        pass

    async def get_initial_status(self, origin, payment_mode):
        if origin == "app" and payment_mode in ["razorpay", "online"]:
            return OrderStatus.DRAFT

        return OrderStatus.OPEN

    async def create_order(self, order_data: Dict, origin: str = "app") -> Dict:
        """Create order - Direct write to database with auto-generated order_id using SQLAlchemy"""

        try:
            logger.info(f"Creating order for customer {order_data['customer_id']}")

            # Validation Layer
            required_fields = ['customer_id', 'customer_name', 'facility_id', 'facility_name', 'total_amount']
            for field in required_fields:
                if field not in order_data or not order_data[field]:
                    raise ValueError(f"Missing required field: {field}")

            # Business Logic Layer (Need to be checked )
            eta = datetime.now() + timedelta(hours=24)

            # Payment Mode
            payment_mode = order_data.get("payment_mode", "cod")
            initial_status = await self.get_initial_status(origin, payment_mode)

            # Generate random prefix for order_id prefix
            random_prefix = generate_random_prefix()

            # Use SQLAlchemy transaction for atomic operation
            with get_raw_transaction() as conn:
                try:
                    # Insert order with SQLAlchemy raw SQL - omit timestamp columns to use database defaults
                    order_insert_sql = """
                        INSERT INTO orders (
                            random_prefix, customer_id, customer_name, 
                            facility_id, facility_name, status, total_amount, eta,
                            order_mode, is_approved
                        ) 
                        VALUES (
                            :random_prefix, :customer_id, :customer_name, 
                            :facility_id, :facility_name, :status, :total_amount, :eta,
                            :order_mode, :is_approved
                        )
                        RETURNING id, order_id, created_at
                    """

                    order_params = {
                        'random_prefix': random_prefix,
                        'customer_id': order_data['customer_id'],
                        'customer_name': order_data['customer_name'],
                        'facility_id': order_data['facility_id'],
                        'facility_name': order_data['facility_name'],
                        'status': initial_status,
                        'total_amount': order_data['total_amount'],
                        'eta': eta,
                        'order_mode': origin,
                        'is_approved': order_data.get('is_approved', False)
                    }

                    result = conn.execute(text(order_insert_sql), order_params)
                    order_row = result.fetchone()
                    
                    if not order_row:
                        raise Exception("Failed to create order")
                    
                    order_internal_id = order_row.id
                    generated_order_id = order_row.order_id
                    created_at = order_row.created_at

                    logger.info(f"Order created with ID: {order_internal_id}, order_id: {generated_order_id}")

                    # Insert order items with corrected foreign key reference (orders.id)
                    if 'items' in order_data and order_data['items']:
                        for item in order_data['items']:
                            item_insert_sql = """
                                INSERT INTO order_items (
                                    order_id, sku, quantity, unit_price, sale_price, status,
                                    cgst, sgst, igst, cess, is_returnable, return_type, return_window, selling_price_net, wh_sku, pack_uom_quantity
                                ) VALUES (
                                    :order_id, :sku, :quantity, :unit_price, :sale_price, :status,
                                    :cgst, :sgst, :igst, :cess, :is_returnable, :return_type, :return_window, :selling_price_net, :wh_sku, :pack_uom_quantity
                                )
                            """

                            item_params = {
                                'order_id': order_internal_id,  # Use primary key, not order_id string
                                'sku': item['sku'],
                                'quantity': item['quantity'],
                                'unit_price': item['unit_price'],
                                'sale_price': item['sale_price'],
                                'cgst': item.get('cgst', 0.0),
                                'sgst': item.get('sgst', 0.0),
                                'igst': item.get('igst', 0.0),
                                'cess': item.get('cess', 0.0),
                                'is_returnable': item.get('is_returnable', False),
                                'return_type': item.get('return_type', '00'),
                                'return_window': item.get('return_window', 0),
                                'selling_price_net': item.get('selling_price_net', 0.0),
                                'status': initial_status,
                                'wh_sku': item.get('wh_sku', ''),
                                'pack_uom_quantity': item.get('pack_uom_quantity', 1)
                            }

                            conn.execute(text(item_insert_sql), item_params)

                    # Insert order address with corrected foreign key reference (orders.id)
                    if 'address' in order_data and order_data['address']:
                        address = order_data['address']
                        address_insert_sql = """
                            INSERT INTO order_addresses (
                                order_id, full_name, phone_number, address_line1, address_line2,
                                city, state, postal_code, country, type_of_address, longitude, latitude
                            ) VALUES (
                                :order_id, :full_name, :phone_number, :address_line1, :address_line2,
                                :city, :state, :postal_code, :country, :type_of_address, :longitude, :latitude
                            )
                        """

                        address_params = {
                            'order_id': order_internal_id,  # Use primary key, not order_id string
                            'full_name': address['full_name'],
                            'phone_number': address['phone_number'],
                            'address_line1': address['address_line1'],
                            'address_line2': address.get('address_line2'),
                            'city': address['city'],
                            'state': address['state'],
                            'postal_code': address['postal_code'],
                            'country': address['country'],
                            'type_of_address': address.get('type_of_address', 'delivery'),
                            'longitude': address.get('longitude'),
                            'latitude': address.get('latitude')
                        }
                        
                        conn.execute(text(address_insert_sql), address_params)

                    # Commit transaction
                    conn.commit()
                    logger.info(f"Order {generated_order_id} created successfully in database")

                    return {
                        "success": True,
                        "message": f"Order {generated_order_id} created successfully",
                        "order_id": generated_order_id,
                        "eta": eta.isoformat(),
                        "created_at": created_at.isoformat() if created_at else None
                    }

                except Exception as db_error:
                    conn.rollback()
                    logger.error(f"Database error creating order: {db_error}")
                    return {
                        "success": False,
                        "message": f"Database error: {str(db_error)}"
                    }

        except ValueError as validation_error:
            logger.error(f"Validation error creating order: {validation_error}")
            return {
                "success": False,
                "message": f"Validation error: {str(validation_error)}"
            }
        except Exception as exc:
            logger.error(f"Unexpected error creating order: {exc}")
            return {
                "success": False,
                "message": f"Unexpected error: {str(exc)}"
            }

    async def update_order_status(self, order_id: str, status) -> Dict:
        """Update order status in both orders and order_items tables using SQLAlchemy"""
        
        try:
            with get_raw_transaction() as conn:
                # Update order status
                update_order_sql = """
                    UPDATE orders 
                    SET status = :status, updated_at = NOW() 
                    WHERE order_id = :order_id
                """

                # Handle both integer constants and string statuses
                if isinstance(status, int):
                    # If it's an integer constant, use it directly
                    status_value = status
                elif isinstance(status, str):
                    # If it's a string, look it up in the mapping
                    status_value = OrderStatus.DB_STATUS_MAP.get(status)
                    if status_value is None:
                        logger.error(f"Unknown status string: {status}")
                        return {
                            "success": False,
                            "message": f"Unknown status: {status}"
                        }
                else:
                    logger.error(f"Invalid status type: {type(status)}")
                    return {
                        "success": False,
                        "message": f"Invalid status type: {type(status)}"
                    }

                result = conn.execute(text(update_order_sql), {
                    'status': status_value,
                    'order_id': order_id
                })

                if result.rowcount == 0:
                    return {
                        "success": False,
                        "message": f"Order {order_id} not found"
                    }

                # Get the primary key for updating items
                get_order_id_sql = "SELECT id FROM orders WHERE order_id = :order_id"
                order_result = conn.execute(text(get_order_id_sql), {'order_id': order_id})
                order_row = order_result.fetchone()
                
                if order_row:
                    # Update all items status using primary key
                    update_items_sql = """
                        UPDATE order_items 
                        SET status = :status 
                        WHERE order_id = :order_pk
                    """

                    conn.execute(text(update_items_sql), {
                        'status': status,
                        'order_pk': order_row.id
                    })

                conn.commit()
                logger.info(f"Order {order_id} status updated to {status}")

                return {
                    "success": True,
                    "message": f"Order {order_id} status updated to {status}"
                }

        except Exception as e:
            logger.error(f"Failed to update order status {order_id}: {e}")
            return {
                "success": False,
                "message": f"Failed to update order status: {str(e)}"
            }

    async def update_item_status(self, order_id: str, sku: str, status: str) -> Dict:
        """Update status of a specific item within an order using SQLAlchemy"""
        
        try:
            with get_raw_transaction() as conn:
                # Get order primary key
                get_order_sql = "SELECT id FROM orders WHERE order_id = :order_id"
                order_result = conn.execute(text(get_order_sql), {'order_id': order_id})
                order_row = order_result.fetchone()
                
                if not order_row:
                    return {
                        "success": False,
                        "message": "Order not found"
                    }
                
                order_pk = order_row.id
                
                # Check if item exists in the order (using primary key)
                check_item_sql = """
                    SELECT id FROM order_items
                    WHERE order_id = :order_pk AND sku = :sku
                """
                
                item_result = conn.execute(text(check_item_sql), {
                    'order_pk': order_pk,
                    'sku': sku
                })
                
                if not item_result.fetchone():
                    return {
                        "success": False,
                        "message": f"Item with SKU '{sku}' not found in order '{order_id}'"
                    }
                
                # Update specific item status
                update_item_sql = """
                    UPDATE order_items
                    SET status = :status, updated_at = NOW()
                    WHERE order_id = :order_pk AND sku = :sku
                """
                
                conn.execute(text(update_item_sql), {
                    'status': status,
                    'order_pk': order_pk,
                    'sku': sku
                })
                
                conn.commit()
                logger.info(f"Item {sku} in order {order_id} status updated to {status}")
                
                return {
                    "success": True,
                    "message": f"Item '{sku}' status updated to '{status}'",
                    "order_id": order_id,
                    "sku": sku,
                    "status": status
                }
                
        except Exception as e:
            logger.error(f"Failed to update item status {order_id}/{sku}: {e}")
            return {
                "success": False,
                "message": f"Failed to update item status: {str(e)}"
            }

