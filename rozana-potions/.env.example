# Database Configuration
DATABASE_NAME=potions
DATABASE_USER=postgres
DATABASE_PASSWORD=rozana^1234
DATABASE_HOST=db1
DATABASE_PORT=5432

# OMS Database Configuration
OMS_DATABASE_HOST=host.docker.internal
OMS_DATABASE_PORT=5432
OMS_DATABASE_NAME=oms_db
OMS_DATABASE_USER=user
OMS_DATABASE_PASSWORD=password

# Redis Configuration
REDIS_BROKER_URL=redis://redis:6379/0

# TMS Configuration
TMS_BASE_URL=https://app.shipsy.in
TMS_API_KEY=your_tms_api_key
TMS_WEBHOOK_TOKEN=static_webhook_token_123

# Invoice Webhook Configuration
INVOICE_WEBHOOK_TOKEN=static_webhook_token_123

# Environment Configuration
APPLICATION_ENVIRONMENT=UAT

# Liink API Configuration
LIINK_BASE_URL=https://liink-backend.stockone.com
LIINK_AUTH_TOKEN=
LIINK_WORKSPACE=ROZANA
LIINK_CONNECTOR_URL=

# Celery Configuration
CELERY_RESULT_EXPIRES=3600
