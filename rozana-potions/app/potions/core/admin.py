from django.contrib import admin
from core.models import Facility, HubMaster

@admin.register(Facility)
class FacilityAdmin(admin.ModelAdmin):
    list_display = ('name', 'is_active', 'created_at', 'updated_at')
    list_filter = ('is_active', 'created_at')
    search_fields = ('name',)
    readonly_fields = ('created_at', 'updated_at')

@admin.register(HubMaster)
class HubMasterAdmin(admin.ModelAdmin):
    """
    Admin interface for HubMaster model.
    """
    list_display = ['hub_code', 'hub_name', 'is_prod', 'is_active', 'created_at']
    list_filter = ['is_prod', 'is_active', 'created_at']
    search_fields = ['hub_code', 'hub_name']
    readonly_fields = ['created_at', 'updated_at']
    ordering = ['hub_code']

    fieldsets = (
        ('Hub Information', {
            'fields': ('hub_code', 'hub_name')
        }),
        ('Configuration', {
            'fields': ('is_prod', 'is_active')
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        })
    )
