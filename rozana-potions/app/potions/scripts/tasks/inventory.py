import logging
import time

from django.utils import timezone
from potions.celery import app as celery_app
from datetime import timedelta
from scripts.models import InventorySync, InventorySyncLog
from integrations.services.wms.inventory_service import WMSInventoryService
from core.models import Facility
from scripts.models import FacilityInventorySync

from integrations.services.transformation.main import transform_wms_inventory_to_lambda_format
from integrations.services.lamda.lamda_wrapper import LambdaWrapper

from integrations.services.wms.auth_service import WMSAPIError


logger = logging.getLogger(__name__)

@celery_app.task(bind=True)
def sync_inventory_task(self, facility_name):
    """
    Celery task to synchronize inventory data from WMS systems.
    This task will be executed by Celery workers.
    
    Args:
        facility_name: Name of specific Facility to process. If None, processes all active syncs.
    """
    logger.info(f"Starting inventory sync task with facility_name: {facility_name}")

    try:
        facility = Facility.objects.get(name=facility_name)
        facility_inventory_sync = FacilityInventorySync.objects.get(facility=facility)
    except Facility.DoesNotExist:
        logger.error(f"Facility with name {facility_name} not found")
        return {"status": "error", "message": f"Facility {facility_name} not found"}
    except FacilityInventorySync.DoesNotExist:
        logger.error(f"FacilityInventorySync for facility {facility_name} not found")
        return {"status": "error", "message": f"FacilityInventorySync for facility {facility_name} not found"}


    inventory_sync_id = facility_inventory_sync.inventory_sync.id
    
    try:
        # Get inventory sync configurations to process
        if facility_inventory_sync.inventory_sync.is_active:
            # Process specific sync configuration
            sync_configs = InventorySync.objects.get(id=inventory_sync_id)

            # get the list of lamda function mapped to this sync configuration
            lambda_functions = sync_configs.target_lambda_integrations.all()
            lambda_functions_conf = []
 
            # get the details of each lamds function mapped to this sync configuration as dict name region and add to list
            for lambda_function in lambda_functions:
                each_lambda_function_conf = {
                    "function_name": lambda_function.function_name,
                    "region": lambda_function.region,
                    "aws_access_key_id": lambda_function.aws_access_key_id,
                    "aws_secret_access_key": lambda_function.aws_secret_access_key
                }
                lambda_functions_conf.append(each_lambda_function_conf)

            # get the list of wms integration mapped to this sync configuration
            wms_integrations = sync_configs.source_integration

            # get the base url from wms integration
            base_url = wms_integrations.base_url
            client_id = wms_integrations.client_id
            client_secret = wms_integrations.client_secret

            # WMSInventoryService instance
            wms_inventory_service = WMSInventoryService(base_url, client_id, client_secret, facility_name)

            # get the inventory data from wms
            inventory_summary = wms_inventory_service.get_inventory_summary()
            total_pages = inventory_summary['total_pages']
            current_page = inventory_summary['current_page']
            estimated_total_items = inventory_summary['estimated_total_items']
            total_items = inventory_summary['total_items']

            #conf
            limit_per_page = 10
            sku_name = None

            if not total_items:
                logger.info("No inventory items found")
                return {"status": "error", "message": "No inventory items found"}
            
            while True:
                if total_pages and current_page > total_pages:
                    break

                try:
                    page_data = wms_inventory_service.get_inventory_page(current_page, limit_per_page, sku_name)
                    inventory_items = page_data['data']
                    page_info = page_data['page_info']

                    #Process the inventory items
                    for item in inventory_items:
                        process_inventory_item(item, lambda_functions_conf, facility_name)


                    print(f"Page {current_page}/{page_info['total_pages']}: {len(inventory_items)} items")
                    logger.info(f"Page {current_page}/{page_info['total_pages']}: {len(inventory_items)} items")

                    # Check if we've reached the last page
                    if current_page >= page_info['total_pages']:
                        logger.info("Reached last page")
                        break

                    time.sleep(1)
                    current_page += 1

                except WMSAPIError as e:
                    logger.error(f"Failed to retrieve page {current_page}: {e}")
                    raise

    except Exception as e:
        logger.error(f"Failed to retrieve inventory: {e}")
        raise



def process_inventory_item(item, lambda_functions_conf, facility_name):

    # transform the inventory item to lambda format
    transformed_item = transform_wms_inventory_to_lambda_format(item, facility_name, encode_data=True)

    # send the transformed item to lambda
    for lambda_function_conf in lambda_functions_conf:
        try:
            function_name = lambda_function_conf['function_name']
            region = lambda_function_conf['region']
            aws_access_key_id = lambda_function_conf['aws_access_key_id']
            aws_secret_access_key = lambda_function_conf['aws_secret_access_key']
            lambda_wrapper = LambdaWrapper(region, aws_access_key_id, aws_secret_access_key)
            lambda_wrapper.push_data_to_lambda_async(function_name, transformed_item)
        except Exception as e:
            logger.error(f"Failed to push data to lambda: {e}")
            


@celery_app.task
def cleanup_old_sync_logs():
    """
    Celery task to clean up old inventory sync logs.
    Keeps logs for the last 30 days only.
    """
    logger.info("Starting cleanup of old sync logs")
    
    cutoff_date = timezone.now() - timedelta(days=30)
    
    deleted_count, _ = InventorySyncLog.objects.filter(
        process_start_time__lt=cutoff_date
    ).delete()
    
    logger.info(f"Cleaned up {deleted_count} old sync log entries")
    return f"Deleted {deleted_count} old log entries"


@celery_app.task
def test_celery_task():
    """
    Simple test task to verify Celery is working correctly.
    """
    logger.info("Test Celery task executed successfully")
    return "Celery is working correctly!"
