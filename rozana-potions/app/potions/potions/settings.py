"""
Django settings for potions project.

Generated by 'django-admin startproject' using Django 5.2.5.

For more information on this file, see
https://docs.djangoproject.com/en/5.2/topics/settings/

For the full list of settings and their values, see
https://docs.djangoproject.com/en/5.2/ref/settings/
"""

from pathlib import Path

# Build paths inside the project like this: BASE_DIR / 'subdir'.
BASE_DIR = Path(__file__).resolve().parent.parent


# Quick-start development settings - unsuitable for production
# See https://docs.djangoproject.com/en/5.2/howto/deployment/checklist/

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = 'django-insecure-ne0k^=o!ae(&e09gyfoxo$sni)km-a+^_fa%@pkj@dhf#q@@-e'

ALLOWED_HOSTS = ['*']


# Application definition

INSTALLED_APPS = [
    'django.contrib.admin',
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.messages',
    'django.contrib.staticfiles',
    'rest_framework',
    'oauth2_provider',
    'corsheaders',
    'django_json_widget',
    'django_celery_beat',
    'core',
    'scripts',
    'integrations',
]

MIDDLEWARE = [
    'oauth2_provider.middleware.OAuth2TokenMiddleware',
    'django.middleware.security.SecurityMiddleware',
    'django.contrib.sessions.middleware.SessionMiddleware',
    'corsheaders.middleware.CorsMiddleware',
    'django.middleware.common.CommonMiddleware',
    'django.middleware.csrf.CsrfViewMiddleware',
    'potions.middlewares.auth_middleware.OAuth2IntegrationMiddleware',
    'potions.middlewares.webhook_middleware.WebhookAuthenticationMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'django.contrib.messages.middleware.MessageMiddleware',
    'django.middleware.clickjacking.XFrameOptionsMiddleware',
]

ROOT_URLCONF = 'potions.urls'

TEMPLATES = [
    {
        'BACKEND': 'django.template.backends.django.DjangoTemplates',
        'DIRS': [],
        'APP_DIRS': True,
        'OPTIONS': {
            'context_processors': [
                'django.template.context_processors.request',
                'django.contrib.auth.context_processors.auth',
                'django.contrib.messages.context_processors.messages',
            ],
        },
    },
]

WSGI_APPLICATION = 'potions.wsgi.application'
ASGI_APPLICATION = 'potions.asgi.application'


# Database
# https://docs.djangoproject.com/en/5.2/ref/settings/#databases

import os
from dotenv import load_dotenv

load_dotenv()

# SECURITY WARNING: don't run with debug turned on in production!
DEBUG = os.getenv('DEBUG', '0') == '1'

DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.postgresql',
        'NAME': os.getenv('DATABASE_NAME', 'potions'),
        'USER': os.getenv('DATABASE_USER', 'postgres'),
        'PASSWORD': os.getenv('DATABASE_PASSWORD', 'rozana^1234'),
        'HOST': os.getenv('DATABASE_HOST', 'db1'),
        'PORT': os.getenv('DATABASE_PORT', '5432'),
    },
    'oms': {
        'ENGINE': 'django.db.backends.postgresql',
        'NAME': os.getenv('OMS_DATABASE_NAME', 'oms'),
        'USER': os.getenv('OMS_DATABASE_USER', 'postgres'),
        'PASSWORD': os.getenv('OMS_DATABASE_PASSWORD', 'rozana^1234'),
        'HOST': os.getenv('OMS_DATABASE_HOST', 'db1'),
        'PORT': os.getenv('OMS_DATABASE_PORT', '5432'),
    }
}

# TMS Configuration
TMS_BASE_URL = os.getenv('TMS_BASE_URL', 'https://app.shipsy.in')
TMS_API_KEY = os.getenv('TMS_API_KEY')
TMS_WEBHOOK_TOKEN = os.getenv('TMS_WEBHOOK_TOKEN', 'static_webhook_token_123')

# Invoice Webhook Configuration
INVOICE_WEBHOOK_TOKEN = os.getenv('INVOICE_WEBHOOK_TOKEN', 'static_webhook_token_123')

# Environment Configuration
APPLICATION_ENVIRONMENT = os.getenv('APPLICATION_ENVIRONMENT', 'UAT')  # UAT or PRODUCTION

# Liink API Configuration
LIINK_BASE_URL = os.getenv('LIINK_BASE_URL', 'https://liink-backend.stockone.com')
LIINK_AUTH_TOKEN = os.getenv('LIINK_AUTH_TOKEN', '')
LIINK_WORKSPACE = os.getenv('LIINK_WORKSPACE', 'ROZANA')
LIINK_CONNECTOR_URL = os.getenv('LIINK_CONNECTOR_URL', '/api/v1/base/connectors/WMS_NEO/SOCreationWithPack/161')

# Password validation
# https://docs.djangoproject.com/en/5.2/ref/settings/#auth-password-validators

AUTH_PASSWORD_VALIDATORS = [
    {
        'NAME': 'django.contrib.auth.password_validation.UserAttributeSimilarityValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.MinimumLengthValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.CommonPasswordValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.NumericPasswordValidator',
    },
]


# Internationalization
# https://docs.djangoproject.com/en/5.2/topics/i18n/

LANGUAGE_CODE = 'en-us'

TIME_ZONE = 'UTC'

USE_I18N = True

USE_TZ = True


# Static files (CSS, JavaScript, Images)
# https://docs.djangoproject.com/en/5.2/howto/static-files/

STATIC_URL = 'static/'

# Default primary key field type
# https://docs.djangoproject.com/en/5.2/ref/settings/#default-auto-field

DEFAULT_AUTO_FIELD = 'django.db.models.BigAutoField'

# Celery Configuration
CELERY_BROKER_URL = os.environ.get('REDIS_BROKER_URL', 'redis://redis:6379')
CELERY_RESULT_BACKEND = os.environ.get('REDIS_BROKER_URL', 'redis://redis:6379')  + "/1"
CELERY_RESULT_EXPIRES = os.environ.get('CELERY_RESULT_EXPIRES', 3600)
CELERY_CACHE_BACKEND = 'default'
REDIS_BROKER_URL = os.environ.get('REDIS_BROKER_URL', 'redis://redis:6379')

# Celery Beat Configuration - Explicit task registration for Django admin
CELERY_BEAT_SCHEDULER = 'django_celery_beat.schedulers:DatabaseScheduler'
CELERY_IMPORTS = [
    'scripts.tasks.inventory',
    'integrations.tasks.invoice_pdf',
    'potions.celery',
]

# Task discovery settings
CELERY_TASK_ALWAYS_EAGER = False
CELERY_TASK_EAGER_PROPAGATES = True


CACHES = {
    'default': {
        'BACKEND': 'django_redis.cache.RedisCache',
        'LOCATION': REDIS_BROKER_URL,
        'OPTIONS': {
            'CLIENT_CLASS': 'django_redis.client.DefaultClient',
        }
    }
}

# Django REST Framework Configuration
REST_FRAMEWORK = {
    'DEFAULT_AUTHENTICATION_CLASSES': (
        'oauth2_provider.contrib.rest_framework.OAuth2Authentication',
        'rest_framework.authentication.SessionAuthentication',
    ),
    'DEFAULT_PERMISSION_CLASSES': [
        'rest_framework.permissions.AllowAny',
    ],
    'DEFAULT_RENDERER_CLASSES': [
        'rest_framework.renderers.JSONRenderer',
    ],
    'DEFAULT_PAGINATION_CLASS': 'rest_framework.pagination.PageNumberPagination',
    'PAGE_SIZE': 20
}

# Django OAuth Toolkit Configuration
OAUTH2_PROVIDER = {
    'ACCESS_TOKEN_EXPIRE_SECONDS': 3600,
    'REFRESH_TOKEN_EXPIRE_SECONDS': 3600 * 24 * 7,  # 7 days
    'AUTHORIZATION_CODE_EXPIRE_SECONDS': 600,
    'ROTATE_REFRESH_TOKEN': True,
}

# CORS Configuration (for frontend integration)
CORS_ALLOWED_ORIGINS = [
    "http://localhost:3000",
    "http://127.0.0.1:3000",
    "http://localhost:8080",
    "http://127.0.0.1:8080",
]

CORS_ALLOW_CREDENTIALS = True
CORS_ALLOW_ALL_ORIGINS = DEBUG  # Only allow all origins in debug mode

# AWS S3 Configuration for Invoice PDFs
AWS_ACCESS_KEY_ID = os.getenv('AWS_ACCESS_KEY_ID', '')
AWS_SECRET_ACCESS_KEY = os.getenv('AWS_SECRET_ACCESS_KEY', '')
AWS_S3_REGION_NAME = os.getenv('AWS_S3_REGION_NAME', 'ap-south-1')
AWS_STORAGE_BUCKET_NAME = os.getenv('AWS_STORAGE_BUCKET_NAME', 'rozana-invoices')


CSRF_TRUSTED_ORIGINS = ['https://*.rozana.in', 'https://*.rozana.tech', 'http://localhost:8004', 'https://*.app.github.dev']