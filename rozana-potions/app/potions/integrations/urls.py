from django.urls import path
from integrations.views.consignments import (
    ForwardConsignmentAPIView,
    ReverseConsignmentAPIView,
    ConsignmentTrackingAPIView
)
from integrations.views.webhooks import TMSConsignmentWebhookView
from integrations.views.invoice_callback import (
    InvoiceCallbackAPIView,
    InvoiceStatusAPIView
)
from integrations.views.orders import (
    WMSOrderCreateAPIView
)
from integrations.views.health import (
    HealthCheckAPIView
)

app_name = 'integrations'

urlpatterns = [

    # Health check endpoint
    path('health/', HealthCheckAPIView.as_view(), name='health_check'),

    # WMS Orchestrator API endpoints
    path('order/create/', WMSOrderCreateAPIView.as_view(), name='wms_order_create'),

    # Orchestrator API endpoints
    path('consignment/create/', ForwardConsignmentAPIView.as_view(), name='create_forward_consignment'),
    path('consignment/create-reverse/', ReverseConsignmentAPIView.as_view(), name='create_reverse_consignment'),
    path('consignment/track/', ConsignmentTrackingAPIView.as_view(), name='track_consignment'),

    # Webhook endpoints
    path('webhooks/tms/consignment/', TMSConsignmentWebhookView.as_view(), name='tms_consignment_webhook'),

    # Invoice callback endpoints with WMS integration name
    path('invoice/callback/<str:wms_integration_name>/', InvoiceCallbackAPIView.as_view(), name='invoice_callback'),
    path('invoice/status/<str:task_id>/', InvoiceStatusAPIView.as_view(), name='invoice_status'),
]
