import logging
import json
from django.conf import settings
from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_http_methods
from django.utils.decorators import method_decorator
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from integrations.models import TMSWebhookEvent

logger = logging.getLogger(__name__)


@method_decorator(csrf_exempt, name='dispatch')
class TMSConsignmentWebhookView(APIView):
    """
    Webhook endpoint to receive TMS consignment status updates.
    
    This endpoint accepts webhook notifications from TMS about consignment
    status changes and stores them for processing.
    """
    
    def post(self, request):
        """
        Handle incoming TMS webhook for consignment status updates.
        
        Expected payload structure (example):
        {
            "reference_number": "REF123456",
            "customer_reference_number": "CUSTOMER_REF_123456",
            "description": "Description of order",
            "courier_partner_reference_number": "123456",
            "courier_partner": "COURIER_PARTNER",
            "courier_account": "COURIER_ACCOUNT",
            "drs_number": "DRS123",
            "shipper_phone": "**********",
            "hub_name": "TEST",
            "hub_code": "TEST",
            "destination_hub_code": "TEST",
            "employee_code": "EMP001",
            "destination_name": "Consignee Name",
            "destination_phone": "**********",
            "destination_address_line_1": "sample address line 1",
            "destination_address_line_2": "sample address line 2",
            "destination_city": "city name",
            "cod_amount": 100,
            "customer_code": "CUSTOMER1",
            "created_at": "2023-03-23 08:10:50",
            "worker_code": "WorkerA",
            "worker_name": "Worker A",
            "attempt_count": 0,
            "poc_image": "image url",
            "poc_image_list": [
                {
                "url": "image1_url"
                },
                {
                "url": "image2_url"
                }
            ],
            "quality_check_image_list": [
                "image1_url",
                "image2_url",
                "image3_url"
            ],
            "signature_image": "image_url",
            "failure_reason": "customer not picking the phone",
            "type": "pickup_awaited",
            "event_time": "2023-03-23T05:39:14.000Z",
            "origin_name": "consignor name",
            "service_type": "PREMIUM",
            "hub_arrival_time": "2023-03-23 08:10:50",
            "pickup_time": "2023-03-23 08:10:50",
            "first_inscan_at_hub_time": "2023-03-23 08:10:50",
            "receiver_phone": "**********",
            "delivery_kyc_type": "document type",
            "delivery_kyc_number": "KYC document number",
            "delivery_status": false,
            "delivery_attempts": 0,
            "delivery_date": "2023-03-23",
            "delivery_time": "8:10:50 am",
            "is_cod": true,
            "creation_date": "2023-03-23 08:10:50",
            "receiver_name": "Receiver Name",
            "receiver_relation": "relation with receiver of order",
            "failure_reason_code": "failure reason code",
            "courier_failure_reason": "courier reason code",
            "lat": 123.456789,
            "lng": 123.456789,
            "rider_phone": "1234567",
            "trip_reference_number": "TRIP123",
            "raven_link": "tracking_link",
            "movement_type": "forward",
            "carrier_location": "location",
            "consignment_movement_type": "forward",
            "shipment_type": "forward",
            "consignment_type": "forward",
            "carrier_name": "COURIER_PARTNER",
            "leg_type": "L1",
            "carrier_location_code": "cmn",
            "carrier_bag_id": "bag123",
            "pickup_time_epoch": 1679539251,
            "hub_arrival_time_epoch": 1679539251,
            "created_at_epoch": 1679539251,
            "event_time_epoch": 1679539251,
            "courier_event_code": "EVENT_CODE",
            "sub_type": "sub_type_code",
            "rescheduled_date": "2023-03-08",
            "rescheduled_timeslot_start": "0900",
            "rescheduled_timeslot_end": "1800",
            "is_otp_verified": true,
            "ordered_quantity": 5,
            "quantity_picked": 4,
            "quantity_delivered": 3,
            "piece_product_code": "ABC",
            "rto_otp": "123321",
            "delivery_otp": "214314",
            "pickup_otp": "123432",
            "pickup_eta": "100",
            "total_eta": "200",
            "notes": "notes",
            "event_source": "event source",
            "eway_bill": {
                "ewb_number": "123456789012",
                "invoice_amount": "304148.870000",
                "invoice_number": "X490130240",
                "invoice_date": "2023-10-31 05:30:00"
            },
            "pieces_detail": [
                {
                "reference_number": "REF123456.001",
                "piece_product_code": "REF78901",
                "description": "Flowers",
                "ordered_quantity": 1,
                "cod_amount": 0,
                "quantity_delivered": 0,
                "is_delivered": false,
                "quantity_picked": 1,
                "is_picked": true
                },
                {
                "reference_number": "REF123456.002",
                "piece_product_code": "REF78902",
                "description": "Staples",
                "ordered_quantity": 1,
                "cod_amount": 0,
                "quantity_delivered": 0,
                "is_delivered": false,
                "quantity_picked": 1,
                "is_picked": true
                }
            ],
            "estimated_delivery_date": "2024-01-03",
            "expected_distance": 300
            }
        """
        try:
            logger.info(f"TMS webhook received from {request.META.get('REMOTE_ADDR', 'unknown')}")
            
            # Parse the webhook payload
            try:
                payload = request.data if hasattr(request, 'data') else json.loads(request.body)
            except json.JSONDecodeError as e:
                logger.error(f"Invalid JSON in webhook payload: {str(e)}")
                return Response({
                    'success': False,
                    'error': 'Invalid JSON payload'
                }, status=status.HTTP_400_BAD_REQUEST)
            
            # Extract required fields from payload
            reference_number = payload.get('reference_number', '')
            
            if not reference_number:
                logger.error(f"Missing reference_number in webhook payload: {payload}")
                return Response({
                    'success': False,
                    'error': 'Missing reference_number in payload'
                }, status=status.HTTP_400_BAD_REQUEST)
            
            # Create webhook event using the simplified model
            webhook_event = TMSWebhookEvent.create_from_webhook(payload)
            
            logger.info(f"TMS webhook received and stored: {webhook_event.type} - {webhook_event.reference_number}")
            
            return Response({
                'success': True,
                'message': 'Webhook received and stored successfully',
                'reference_number': webhook_event.reference_number,
                'type': webhook_event.type,
                'event_time': webhook_event.event_time.isoformat()
            }, status=status.HTTP_200_OK)
            
        except Exception as e:
            logger.error(f"Error processing TMS webhook: {str(e)}", exc_info=True)
            return Response({
                'success': False,
                'error': 'Internal server error processing webhook'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

