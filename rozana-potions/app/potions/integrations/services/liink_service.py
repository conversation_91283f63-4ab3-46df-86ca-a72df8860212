import json
import httpx
import logging
from datetime import datetime, timedelta
from typing import Dict, Optional, Any
from django.conf import settings
from django.db import connections
from core.models import Facility

logger = logging.getLogger(__name__)


class LiinkService:
    """Service for integrating with Liink API"""

    def __init__(self):
        self.base_url = getattr(settings, 'LIINK_BASE_URL', 'https://liink-backend.stockone.com')
        self.auth_token = getattr(settings, 'LIINK_AUTH_TOKEN', '')
        self.workspace = getattr(settings, 'LIINK_WORKSPACE', 'ROZANA')
        self.connector_url = getattr(settings, 'LIINK_CONNECTOR_URL', '/api/v1/base/connectors/WMS_NEO/SOCreationWithPack/161')
        self.timeout = 60.0

    def _get_liink_order_type(self, order_mode: str) -> str:
        """Helper function to map OMS order_mode to Liink order type"""
        order_type_mapping = {
            'app': 'Express',
            'pos': 'POS_EXPRESS',
            'web': 'Express'
        }
        return order_type_mapping.get(order_mode, 'Express')

    def _validate_facility_wms_access(self, facility_name: str) -> bool:
        """Validate if WMS integration is enabled for the facility"""
        try:
            facility = Facility.objects.get(name=facility_name)
            if not facility.can_create_wms_orders():
                logger.error(f"WMS integration is disabled for facility: {facility_name}")
                return False
            return True
        except Facility.DoesNotExist:
            logger.error(f"Facility not found: {facility_name}")
            return False
        except Exception as e:
            logger.error(f"Error validating facility WMS access: {str(e)}")
            return False

    def fetch_order_data_from_oms(self, order_id: str) -> Optional[Dict[str, Any]]:
        """Fetch complete order data from OMS database"""
        try:
            oms_db = connections['oms']
            cursor = oms_db.cursor()

            # Fetch order details
            order_query = """
                SELECT o.order_id, o.customer_id, o.customer_name, o.facility_id, o.facility_name,
                       o.status, o.total_amount, o.eta, o.created_at, o.order_mode
                FROM orders o
                WHERE o.order_id = %s
            """

            cursor.execute(order_query, [order_id])
            order_result = cursor.fetchone()

            if not order_result:
                logger.error(f"Order {order_id} not found in OMS database")
                return None

            # Get column names
            columns = [col[0] for col in cursor.description]
            order_data = dict(zip(columns, order_result))

            # Fetch order items with pack_uom_quantity
            items_query = """
                SELECT oi.id, oi.sku, oi.wh_sku, oi.quantity, oi.unit_price, oi.sale_price, 
                       oi.status, oi.pack_uom_quantity
                FROM order_items oi
                JOIN orders o ON oi.order_id = o.id
                WHERE o.order_id = %s
            """

            cursor.execute(items_query, [order_id])
            items_result = cursor.fetchall()
            items_columns = [col[0] for col in cursor.description]

            items = []
            for item_row in items_result:
                item_dict = dict(zip(items_columns, item_row))
                items.append({
                    "id": item_dict["id"],
                    "sku": item_dict["sku"],
                    "wh_sku": item_dict["wh_sku"],
                    "quantity": item_dict["quantity"],
                    "unit_price": float(item_dict["unit_price"]) if item_dict["unit_price"] else 0,
                    "sale_price": float(item_dict["sale_price"]) if item_dict["sale_price"] else 0,
                    "pack_uom_quantity": item_dict["pack_uom_quantity"] or 1,
                    "status": item_dict["status"]
                })

            # Fetch order address
            address_query = """
                SELECT oa.full_name, oa.phone_number, oa.address_line1, oa.address_line2,
                       oa.city, oa.state, oa.postal_code, oa.country, oa.type_of_address,
                       oa.longitude, oa.latitude
                FROM order_addresses oa
                JOIN orders o ON oa.order_id = o.id
                WHERE o.order_id = %s
                LIMIT 1
            """

            cursor.execute(address_query, [order_id])
            address_result = cursor.fetchone()
            address = {}

            if address_result:
                address_columns = [col[0] for col in cursor.description]
                address_dict = dict(zip(address_columns, address_result))
                address = {
                    "full_name": address_dict["full_name"],
                    "phone_number": address_dict["phone_number"],
                    "address_line1": address_dict["address_line1"],
                    "address_line2": address_dict["address_line2"] or "",
                    "city": address_dict["city"],
                    "state": address_dict["state"],
                    "postal_code": address_dict["postal_code"],
                    "country": address_dict["country"],
                    "type_of_address": address_dict["type_of_address"],
                    "longitude": float(address_dict["longitude"]) if address_dict["longitude"] else 0.0,
                    "latitude": float(address_dict["latitude"]) if address_dict["latitude"] else 0.0
                }

            # Construct complete order data
            complete_order_data = {
                "order_id": order_data["order_id"],
                "customer_id": order_data["customer_id"],
                "customer_name": order_data["customer_name"],
                "facility_id": order_data["facility_id"],
                "facility_name": order_data["facility_name"],
                "status": order_data["status"],
                "total_amount": float(order_data["total_amount"]) if order_data["total_amount"] else 0,
                "created_at": order_data["created_at"].isoformat() if order_data["created_at"] else None,
                "eta": order_data["eta"].isoformat() if order_data["eta"] else None,
                "order_mode": order_data["order_mode"],
                "items": items,
                "address": address
            }

            logger.info(f"Fetched order data from OMS DB for order {order_id}")
            return complete_order_data

        except Exception as e:
            logger.error(f"Error fetching order data from OMS DB for order {order_id}: {str(e)}")
            return None
        finally:
            if 'cursor' in locals():
                cursor.close()

    def transform_order_to_liink_format(self, order_data: Dict[str, Any]) -> Dict[str, Any]:
        """Transform OMS order data to Liink API format"""

        address = order_data.get("address", {})

        # Calculate time slots (default calculation since these fields don't exist in OMS)
        now = datetime.now()
        default_promised = now + timedelta(hours=24)
        slot_from = (now + timedelta(hours=23)).strftime("%H:%M:%S")
        slot_to = default_promised.strftime("%H:%M:%S")
        promised_time = default_promised.strftime("%Y-%m-%d %H:%M:%S")

        # Transform items for Liink
        liink_items = []
        for item in order_data.get("items", []):
            # Use sale_price as both mrp and sale_price_gst (no discount calculation since discount_amount doesn't exist)
            sale_price = item.get("sale_price", 0)
            unit_price = item.get("unit_price", 0)

            liink_items.append({
                "aux_data": {
                    "pack_uom_quantity": item.get("pack_uom_quantity", 1)
                },
                "sku": item.get("wh_sku"),
                "line_reference": str(item.get("id")),
                "quantity": item.get("quantity"),
                "mrp": unit_price,
                "discount_amount": max(0, unit_price - sale_price),
                "sale_price_gst": sale_price
            })

        # Determine order type using helper function
        order_mode = order_data.get("order_mode", "pos")
        order_type = self._get_liink_order_type(order_mode)

        # Build Liink payload
        liink_payload = {
            "order_type": order_type,
            "slot_from": slot_from,
            "slot_to": slot_to,
            "promised_time": promised_time,
            "warehouse": order_data.get("facility_name"),
            "customer": {
                "customer_reference": str(order_data.get("customer_id", "")),
                "customer_name": address.get("full_name", order_data.get("customer_name", "")),
                "address_line_1": address.get("address_line1", ""),
                "address_line_2": address.get("address_line2", ""),
                "state": address.get("state", ""),
                "city": address.get("city", ""),
                "pincode": address.get("postal_code", ""),
                "ship_group": "1"
            },
            "order_reference": order_data.get("order_id"),
            "items": liink_items,
        }

        # add auto_pick_and_invoice only if its pos order
        if order_mode == "pos":
            liink_payload["auto_pick_and_invoice"] = "true"


        logger.info(f"Transformed order {order_data.get('order_id')} to Liink format")
        logger.info(f"Liink payload: {liink_payload}")
        return liink_payload

    async def create_order_in_liink(self, payload: Dict[str, Any], warehouse: str) -> Dict[str, Any]:
        """Create order in Liink via API"""
        try:
            headers = {
                'Content-Type': 'application/json',
                'Authorization': f'Token {self.auth_token}',
                'workspace': self.workspace
            }

            # Use the warehouse-specific endpoint
            endpoint = f"{self.base_url}{self.connector_url}"

            async with httpx.AsyncClient(timeout=self.timeout) as client:
                logger.info(f"Sending order to Liink: {endpoint}")
                logger.info(f"Payload: {json.dumps(payload, indent=2)}")

                response = await client.post(
                    endpoint,
                    headers=headers,
                    json=payload
                )

                if response.status_code in [200, 201]:
                    response_data = response.json() if response.content else {}
                    logger.info(f"Order created successfully in Liink: {response_data}")
                    return {
                        "success": True,
                        "liink_response": response_data,
                        "message": "Order created successfully in Liink"
                    }
                else:
                    error_msg = response.text
                    logger.error(f"Liink API error: {response.status_code} - {error_msg}")
                    return {
                        "success": False,
                        "error": f"Liink API error: {response.status_code}",
                        "message": error_msg
                    }

        except Exception as e:
            logger.error(f"Exception while creating order in Liink: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "message": "Exception occurred while creating order in Liink"
            }


    def update_oms_order_status(self, order_id: str, status: int) -> bool:
        """Update order status in OMS database"""
        try:
            before_status_key = ""
            oms_db = connections['oms']
            cursor = oms_db.cursor()

            get_status_query = "SELECT status FROM orders WHERE order_id = %s"
            cursor.execute(get_status_query, [order_id])
            before_status = cursor.fetchone()
            if before_status:
                before_status_key = before_status[0]

            update_query = """
                UPDATE orders 
                SET status = %s, updated_at = NOW()
                WHERE order_id = %s
            """

            cursor.execute(update_query, [status, order_id])
            oms_db.commit()

            logger.info(f"Updated order {order_id} status from {before_status_key} to {status} in OMS")
            return True

        except Exception as e:
            logger.error(f"Error updating order status in OMS for order {order_id}: {str(e)}")
            return False
        finally:
            if 'cursor' in locals():
                cursor.close()
