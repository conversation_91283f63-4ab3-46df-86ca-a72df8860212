import logging
import requests
from typing import Dict, Any, List
from django.conf import settings
from integrations.services.tms.auth_service import make_tms_request, TMSAPIError
from core.models import HubMaster

logger = logging.getLogger(__name__)


class BaseTMSCNService:
    """
    Base service class for TMS Consignment Note operations.
    
    Provides common functionality for creating consignment notes via TMS API.
    """
    
    def validate_hub(self, hub_code: str) -> None:
        """
        Validate if the hub is allowed for consignment creation based on environment.
        
        Args:
            hub_code: The hub code to validate
            
        Raises:
            TMSAPIError: If the hub is not allowed
        """
        environment = getattr(settings, 'APPLICATION_ENVIRONMENT', 'PRODUCTION')
        is_allowed, error_message = HubMaster.is_hub_allowed(hub_code, environment)
        
        if not is_allowed:
            logger.error(f"Hub validation failed: {error_message}")
            raise TMSAPIError(error_message)
        
        logger.info(f"Hub '{hub_code}' validated successfully for environment '{environment}'")
    
    def create_consignment(self, consignment_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Create a consignment note via TMS API.
        
        Args:
            consignment_data: Dictionary containing consignment details
            
        Returns:
            Dict containing the API response
            
        Raises:
            TMSAPIError: If the API request fails
        """
        endpoint = "/api/client/integration/consignment/upload/softdata/v2"
        
        # Validate hub before creating consignment
        hub_code = consignment_data.get('hub_code')
        if hub_code:
            self.validate_hub(hub_code)
        
        try:
            logger.info(f"Creating consignment with reference: {consignment_data.get('reference_number', 'N/A')}")
            
            response = make_tms_request(
                method='POST',
                endpoint=endpoint,
                json=consignment_data
            )
            
            result = response.json()
            logger.info(f"Consignment created successfully: {result}")
            return result
            
        except Exception as e:
            logger.error(f"Failed to create consignment: {str(e)}")
            raise TMSAPIError(f"Failed to create consignment: {str(e)}") from e


class ForwardCNService(BaseTMSCNService):
    """
    Service class for Forward Consignment Note (CN) operations.
    
    Handles delivery consignments from warehouse to customer.
    """
    
    def create(
        self,
        customer_code: str,
        reference_number: str,
        customer_reference_number: str,
        hub_code: str,
        cod_amount: float,
        destination_details: Dict[str, Any],
        pieces_detail_list: List[Dict[str, Any]],
        cod_favor_of: str = "Rozana",
        cod_collection_mode: str = "",
    ) -> Dict[str, Any]:
        """
        Create a forward consignment (delivery from warehouse to customer).
        
        Args:
            customer_code: Customer code (e.g., "Rozana")
            reference_number: Unique reference number for the consignment
            customer_reference_number: Customer's internal reference number
            hub_code: Hub code for pickup/delivery
            cod_amount: Cash on delivery amount
            destination_details: Dictionary containing delivery address and contact info
            pieces_detail: List of items/pieces in the consignment
            cod_favor_of: COD collection entity (default: "Rozana")
            cod_collection_mode: COD collection mode (default: "")
            reference_image_url: Optional reference image URL
            customs_value: Optional customs value information
            
        Returns:
            Dictionary containing the API response
        """
        consignment_data = {
            "customer_code": customer_code,
            "service_type_id": "PREMIUM",
            "action_type": "delivery",
            "consignment_type": "forward",
            "movement_type": "forward",
            "verify_otp_on_delivery": True,
            "reference_number": reference_number,
            "cod_favor_of": cod_favor_of,
            "cod_collection_mode": cod_collection_mode,
            "cod_amount": cod_amount,
            "currency": "INR",
            "customer_reference_number": customer_reference_number,
            "hub_code": hub_code,
            "reference_image_url" : "https://shipsy-public-assets.s3.amazonaws.com/shipsyflamingo/logo.png",
            "customs_value" : {
                "amount" : "100",
                "currency" : "USD" # have to check the significance of this field
            },
            "destination_details": {
                "company_name" : destination_details['company_name'],
                "name" : destination_details['name'],
                "phone" : destination_details['phone'],
                "address_line_1" : destination_details['address_line_1'],
                "pincode" : destination_details['pincode'],
                "district" : destination_details['district'],
                "city" : destination_details['city'],
                "state" : destination_details['state'],
                "country" : destination_details['country'],
                "latitude" : destination_details.get('latitude'),
                "longitude" : destination_details.get('longitude')
            }
        }

        pieces_detail = []
        for piece in pieces_detail_list:
            details = {
                "description" : piece.get('description', ''),
                "quantity" : piece['quantity'],
                "declared_value" : piece['declared_value'],
                "piece_product_code" : piece['piece_product_code'],
                "cod_amount" : piece['cod_amount'],
                "reference_image_url" : piece['reference_image_url'],
            }
            pieces_detail.append(details)
        
        consignment_data['pieces_detail'] = pieces_detail
        return self.create_consignment(consignment_data)

class TrackCNService(BaseTMSCNService):
    """
    Service class for Track Consignment Note (CN) operations.
    """
    
    def track(self, reference_number: str) -> Dict[str, Any]:
        """
        Track a consignment (delivery from warehouse to customer).
        
        Args:
            reference_number: Unique reference number for the consignment
            
        Returns:
            Dictionary containing the API response
        """
        endpoint = "/api/client/integration/consignment/track"
        consignment_data = {
            "reference_number": reference_number,
        }
        try:
            logger.info(f"Tracking consignment with reference: {consignment_data.get('reference_number', 'N/A')}")
            
            response = make_tms_request(
                method='GET',
                endpoint=endpoint,
                params=consignment_data
            )
            
            result = response.json()
            logger.info(f"Consignment tracked successfully: {result}")
            return result
            
        except Exception as e:
            logger.error(f"Failed to track consignment: {str(e)}")
            raise TMSAPIError(f"Failed to track consignment: {str(e)}") from e

class ReverseCNService(BaseTMSCNService):
    """
    Service class for Reverse Consignment Note (CN) operations.
    
    Handles return consignments from customer to warehouse.
    """
    
    def create(
        self,
        customer_reference_number: str,
        hub_code: str,
        hub_address_line_1: str,
        origin_details: Dict[str, Any],
        pieces_detail_list: List[Dict[str, Any]],
        return_code: str = "",
    ) -> Dict[str, Any]:
        """
        Create a reverse consignment (return from customer to warehouse).
        
        Args:
            reference_number: Unique reference number for the consignment
            customer_reference_number: Customer's internal reference number
            hub_code: Hub code for pickup/delivery
            pickup_details: Dictionary containing pickup address and contact info (customer location)
            destination_details: Dictionary containing destination address (warehouse/hub)
            pieces_detail_list: List of items/pieces being returned
            
        Returns:
            Dictionary containing the API response
        """
        consignment_data = {
            "customer_code": "Rozana",
            "service_type_id": "PREMIUM",
            "action_type": "single_pickup",
            "consignment_type": "reverse",
            "movement_type": "forward",
            "verify_otp_on_delivery": True,
            "reference_number": return_code,
            "cod_favor_of": "Rozana",
            "cod_collection_mode": "", # cash
            "currency": "INR",
            "customer_reference_number": customer_reference_number, # Order ID/ Invoice Number
            "hub_code": hub_code,
            "origin_details": {
                "company_name": "Rozana",
                "name": origin_details['name'],
                "phone": origin_details['phone'],
                "address_line_1": origin_details['address_line_1'],
                "pincode": origin_details['pincode'],
                "district": origin_details['district'],
                "city": origin_details['city'],
                "state": origin_details['state'],
                "country": origin_details['country'],
                "latitude": origin_details.get('latitude'),
                "longitude": origin_details.get('longitude')
            },
            "destination_details": {
                "address_hub_code": hub_code,
                "address_line_1": hub_address_line_1, 
            },
        }

        # Create pieces detail
        pieces_detail = []
        reference_image_url = "https://dev.rozana.in/public/uploads/products/thumbnail/500x500.png"
        payable_amount = 0
        for piece in pieces_detail_list:
            return_quantity = piece['return_quantity']
            details = {
                "description" : piece.get('description', ''),
                "quantity" : return_quantity,
                "declared_value" : piece['declared_value'], # mrp * return_quantity
                "piece_product_code" : piece['piece_product_code'],
                "cod_amount" : piece['cod_amount'], # unit_price * return_quantity
                "reference_image_url" : reference_image_url,
            }
            payable_amount += piece['cod_amount']
            pieces_detail.append(details)

        # Add pieces detail to consignment data and final payable amount
        consignment_data['pieces_detail'] = pieces_detail
        consignment_data['cod_amount'] = payable_amount
        return self.create_consignment(consignment_data)

