import requests
import logging
from typing import Dict
import os

logger = logging.getLogger(__name__)


class TMSAPIError(Exception):
    """Raised when TMS API calls fail"""
    pass


def get_tms_config():
    """
    Get TMS configuration from environment variables only.
    
    Returns:
        tuple: (base_url, api_key)
    """
    base_url = os.getenv('TMS_BASE_URL', 'https://app.shipsy.in')
    api_key = os.getenv('TMS_API_KEY')
    
    if not api_key:
        raise TMSAPIError("TMS_API_KEY environment variable is required")
    
    return base_url.rstrip('/'), api_key


def make_tms_request(method: str, endpoint: str, **kwargs) -> requests.Response:
    """
    Make an authenticated request to the TMS API.
    
    Args:
        method: HTTP method (GET, POST, etc.)
        endpoint: API endpoint (without base URL)
        **kwargs: Additional arguments for requests
        
    Returns:
        Response object
        
    Raises:
        TMSAPIError: If the request fails
    """
    base_url, api_key = get_tms_config()
    url = f"{base_url}/{endpoint.lstrip('/')}"
    
    # Set authentication headers
    headers = kwargs.pop('headers', {})
    headers.update({
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'Api-Key': api_key
    })
    
    try:
        logger.info(f"Making {method} request to {url}")
        response = requests.request(method, url, headers=headers, **kwargs)
        
        # Log response details
        logger.info(f"Response status: {response.status_code}")
        if response.status_code >= 400:
            logger.error(f"TMS API error: {response.status_code} - {response.text}")
            response.raise_for_status()
            
        return response
        
    except requests.exceptions.RequestException as e:
        logger.error(f"TMS API request failed: {str(e)}")
        raise TMSAPIError(f"TMS API request failed: {str(e)}") from e
