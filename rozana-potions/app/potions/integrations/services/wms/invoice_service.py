import logging
from datetime import datetime
from django.conf import settings
from django.utils import timezone
import io
from integrations.models import WMSIntegration
from integrations.services.wms.auth_service import WMSAuth
from integrations.services.aws.s3_service import S3Service
from integrations.services.oms.invoice_update import OMSInvoiceUpdateService

from integrations.services.wms.auth_service import WMSAuthenticationError, WMSAPIError

logger = logging.getLogger(__name__)


class InvoiceService:
    """
    Service to handle invoice processing: fetch HTML, convert to PDF, and upload to S3.
    """

    def __init__(self, wms_integration_name='default'):
        # Initialize S3 service
        self.s3_service = S3Service()
        # Initialize OMS invoice update service
        self.oms_service = OMSInvoiceUpdateService()
        # Initialize WMS authentication
        self._init_wms_auth(wms_integration_name)

    def _init_wms_auth(self, integration_name):
        """
        Initialize WMS authentication using database configuration.
        
        Args:
            integration_name (str): Name of the WMS integration to use
        """
        try:
            # Get WMS integration configuration from database
            wms_config = WMSIntegration.objects.filter(
                name=integration_name,
                is_active=True
            ).first()
            if not wms_config:
                # Fallback to first active WMS integration
                wms_config = WMSIntegration.objects.filter(is_active=True).first()

            if not wms_config:
                raise ValueError("No active WMS integration found")

            # Initialize WMS auth with database configuration
            self.wms_auth = WMSAuth(
                base_url=wms_config.base_url,
                client_id=wms_config.client_id,
                client_secret=wms_config.client_secret
            )
            self.wms_base_url = wms_config.base_url
            logger.info(f"Initialized WMS authentication with integration: {wms_config.name}")
        except Exception as e:
            logger.error(f"Failed to initialize WMS authentication: {str(e)}")
            raise ValueError(f"WMS authentication initialization failed: {str(e)}")

    def fetch_invoice_html(self, order_reference, warehouse):
        """
        Fetch invoice HTML from StockOne API using WMS authentication.
        Args:
            order_reference (str): Order reference number
            warehouse (str): Warehouse name
        Returns:
            str: HTML content of the invoice
        """
        endpoint = "/api/v1/outbound/print_invoice/"
        params = {
            'order_reference': order_reference,
            'external_call': 'true',
            'order_type': 'normal'
        }

        # Additional headers specific to print invoice API
        additional_headers = {
            'accept': 'application/json, text/plain, */*',
            'accept-language': 'en-GB,en-US;q=0.9,en;q=0.8',
            'warehouse': warehouse,
        }

        try:
            logger.info(f"Fetching invoice HTML for order: {order_reference}, warehouse: {warehouse}")
            if not self.wms_auth:
                raise ValueError("WMS authentication not available")
            # Use WMS authentication
            response = self.wms_auth.make_authenticated_request(
                method='GET',
                endpoint=endpoint,
                headers=additional_headers,
                params=params
            )
            # Validate response content type
            content_type = response.headers.get('content-type', '')
            if content_type.startswith('text/html'):
                logger.info(f"Successfully fetched invoice HTML for order: {order_reference}")
                return response.text
            else:
                logger.error(f"Unexpected content type: {content_type}")
                raise ValueError(f"Expected HTML content, got {content_type}")
        except (WMSAuthenticationError, WMSAPIError) as e:
            logger.error(f"WMS API error fetching invoice HTML: {str(e)}")
            raise
        except requests.exceptions.RequestException as e:
            logger.error(f"Error fetching invoice HTML: {str(e)}")
            raise
        except Exception as e:
            logger.error(f"Unexpected error fetching invoice HTML: {str(e)}")
            raise

    def convert_html_to_pdf(self, html_content):
        """
        Convert HTML to PDF using WeasyPrint.
        Args:
            html_content (str): HTML content to convert
        Returns:
            bytes: PDF content as bytes
        """
        try:
            from weasyprint import HTML, CSS
            logger.info("Converting HTML to PDF using WeasyPrint")
            # Add CSS for better table styling
            css_styles = CSS(string="""
                @page {
                    size: 297mm 210mm;
                    margin: 1cm;
                }
                body {
                    font-family: Arial, sans-serif;
                    font-size: 12px;
                    line-height: 1.4;
                }
                table {
                    border-collapse: collapse;
                    width: 100%;
                    font-size: 11px;
                }
                th, td {
                    border: 0.5px solid #666;
                    padding: 4px 6px;
                    text-align: left;
                    vertical-align: top;
                }
                th {
                    background-color: #f8f8f8;
                    font-weight: bold;
                }
            """)
            html_doc = HTML(string=html_content)
            pdf_buffer = io.BytesIO()
            html_doc.write_pdf(pdf_buffer, stylesheets=[css_styles])
            pdf_buffer.seek(0)

            logger.info("PDF generated successfully using WeasyPrint")
            return pdf_buffer.getvalue()

        except ImportError:
            logger.error("WeasyPrint not installed. Please install: pip install weasyprint")
            raise Exception("WeasyPrint library not available. Install with: pip install weasyprint")
        except Exception as e:
            logger.error(f"Error converting HTML to PDF with WeasyPrint: {str(e)}")
            raise

    def upload_pdf_to_s3(self, pdf_content, warehouse, invoice_date, order_reference):
        """
        Upload PDF to S3 bucket with organized folder structure.
        Args:
            pdf_content (bytes): PDF content as bytes
            warehouse (str): Warehouse name
            invoice_date (datetime): Invoice date
            order_reference (str): Order reference number
        Returns:
            dict: Dict containing 'key' and 'url' of uploaded PDF
        """
        try:
            logger.info(f"Uploading PDF to S3 for order: {order_reference}")

            # Use S3Service to upload invoice PDF
            s3_result = self.s3_service.upload_invoice_pdf(
                pdf_content=pdf_content,
                warehouse=warehouse,
                order_reference=order_reference,
                invoice_date=invoice_date,
                additional_metadata={
                    'uploaded_at': timezone.now().isoformat()
                }
            )
            logger.info(f"PDF uploaded successfully: {s3_result['url']}")
            return s3_result

        except Exception as e:
            logger.error(f"Error uploading PDF to S3: {str(e)}")
            raise

    def update_oms_invoice_key(self, order_id, invoice_key):
        """
        Update the invoice key in the OMS database for a specific order.
        Args:
            order_id (str): The order ID to update (computed order_id from OMS)
            invoice_key (str): The S3 key of the generated invoice PDF
        Returns:
            dict: Result dictionary with success status and details
        """
        try:
            logger.info(f"Updating OMS database for order {order_id} with invoice key: {invoice_key}")

            result = self.oms_service.update_order_invoice_key_raw(order_id, invoice_key)

            if result['success']:
                logger.info(f"Successfully updated OMS database for order {order_id}")
            else:
                logger.warning(f"Failed to update OMS database for order {order_id}: {result.get('error')}")

            return result

        except Exception as e:
            logger.error(f"Error updating OMS database for order {order_id}: {str(e)}")
            return {
                'success': False,
                'error': f'OMS update failed: {str(e)}',
                'order_id': order_id
            }

    def process_invoice(self, order_reference, warehouse, invoice_date=None, update_oms=True):
        """
        Complete invoice processing workflow: fetch HTML, convert to PDF, upload to S3, and update OMS.
        Args:
            order_reference (str): Order reference number (same as order_id in OMS)
            warehouse (str): Warehouse name
            invoice_date (datetime, optional): Invoice date. Defaults to current time.
            update_oms (bool, optional): Whether to update OMS database. Defaults to True.
        Returns:
            dict: Processing result with S3 URL and OMS update status
        """
        if invoice_date is None:
            invoice_date = timezone.now()
        try:
            # Step 1: Fetch invoice HTML
            html_content = self.fetch_invoice_html(order_reference, warehouse)

            # Step 2: Convert HTML to PDF
            pdf_content = self.convert_html_to_pdf(html_content)

            # Step 3: Upload PDF to S3
            s3_result = self.upload_pdf_to_s3(pdf_content, warehouse, invoice_date, order_reference)

            # Step 4: Update OMS database with invoice key (using order_reference as order_id)
            oms_result = None
            if update_oms:
                oms_result = self.update_oms_invoice_key(order_reference, s3_result['key'])

            logger.info(f"Invoice processing completed successfully for {order_reference}")

            return {
                'success': True,
                'order_reference': order_reference,
                's3_url': s3_result['url'],
                's3_key': s3_result['key'],
                'oms_update': oms_result
            }
        except Exception as e:
            logger.error(f"Invoice processing failed for {order_reference}: {str(e)}")
            raise


# Utility function for external use
def process_invoice_async(order_reference, warehouse, invoice_date=None, wms_integration_name='default', update_oms=True):
    """
    Utility function to process invoice asynchronously.
    This can be called from Celery tasks.

    Args:
        order_reference (str): Order reference number (same as order_id in OMS)
        warehouse (str): Warehouse name
        invoice_date (datetime, optional): Invoice date
        wms_integration_name (str): Name of WMS integration to use
        update_oms (bool, optional): Whether to update OMS database. Defaults to True.
    """
    service = InvoiceService(wms_integration_name=wms_integration_name)
    return service.process_invoice(order_reference, warehouse, invoice_date, update_oms)
