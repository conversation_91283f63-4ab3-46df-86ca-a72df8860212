import logging
from typing import Dict, List, Optional, Any
from integrations.services.wms.auth_service import W<PERSON><PERSON><PERSON>, WMSAPIError
import requests

logger = logging.getLogger(__name__)


class WMSInventoryService:
    """
    Service class for WMS inventory operations with paginated inventory retrieval.
    
    This class handles:
    - Paginated inventory data retrieval
    - Inventory search and filtering
    - Error handling for inventory operations
    
    Uses WMSAuth for authentication functionality.
    """
    
    def __init__(self, base_url: str, client_id: str, client_secret: str, warehouse: str):
        """
        Initialize WMS inventory service.
        
        Args:
            base_url: Base URL for the WMS API (e.g., 'https://rzn1-be.stockone.com')
            client_id: OAuth2 client ID
            client_secret: OAuth2 client secret
            warehouse: Warehouse identifier for inventory requests
        """
        self.warehouse = warehouse
        self.auth = WMSAuth(base_url, client_id, client_secret)
    
    def _make_authenticated_request(self, method: str, endpoint: str, **kwargs) -> requests.Response:
        """
        Make an authenticated request to the WMS API with warehouse header.
        
        Args:
            method: HTTP method (GET, POST, etc.)
            endpoint: API endpoint (without base URL)
            **kwargs: Additional arguments for requests
            
        Returns:
            Response object
            
        Raises:
            WMSAPIError: If the request fails
        """
        headers = kwargs.pop('headers', {})
        headers.update({
            'warehouse': self.warehouse
        })
        
        return self.auth.make_authenticated_request(method, endpoint, headers=headers, **kwargs)

    def get_inventory_page(self, page: int = 1, limit: int = 100, sku_name: Optional[str] = None) -> Dict[str, Any]:
        """
        Retrieve a single page of inventory data.

        Args:
            page: Page number (1-based)
            limit: Number of items per page
            sku_name: SKU name to search for (case-insensitive)
        Returns:
            Dictionary containing inventory data with pagination info

        Raises:
            WMSAPIError: If the API request fails
        """
        logger.info(f"Fetching inventory page {page} with limit {limit}")

        params = {
            'limit': limit,
            'offset': (page - 1) * limit
        }

        if sku_name:
            params['sku'] = sku_name

        try:
            response = self._make_authenticated_request(
                'GET',
                '/api/v1/inventory/inventory/',
                params=params
            )

            data = response.json()
            
            # Validate response structure
            if 'data' not in data or 'page_info' not in data:
                raise WMSAPIError("Invalid response structure")
            
            logger.info(f"Successfully retrieved {len(data['data'])} inventory items")
            return data
            
        except (ValueError, KeyError) as e:
            logger.error(f"Invalid inventory response format: {e}")
            raise WMSAPIError(f"Invalid response format: {e}")
    
    def get_all_inventory(self, limit_per_page: int = 100, max_pages: Optional[int] = None, sku_name: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        Retrieve all inventory data across multiple pages.
        
        Args:
            limit_per_page: Number of items per page request
            max_pages: Maximum number of pages to fetch (None for all)
            sku_name: SKU name to search for (case-insensitive)
            
        Returns:
            List of all inventory items
            
        Raises:
            WMSAPIError: If any API request fails
        """
        logger.info(f"Starting full inventory retrieval (limit_per_page={limit_per_page}, max_pages={max_pages})")

        all_inventory = []
        current_page = 1

        while True:
            # Check max_pages limit
            if max_pages and current_page > max_pages:
                logger.info(f"Reached maximum page limit: {max_pages}")
                break

            try:
                page_data = self.get_inventory_page(current_page, limit_per_page, sku_name)
                inventory_items = page_data['data']
                page_info = page_data['page_info']
                
                # Add items to our collection
                all_inventory.extend(inventory_items)

                #Process the inventory items
                for item in inventory_items:
                    print(item) # call the process_inventory_item function here

                logger.info(f"Page {current_page}/{page_info['total_pages']}: {len(inventory_items)} items")

                # Check if we've reached the last page
                if current_page >= page_info['total_pages']:
                    logger.info("Reached last page")
                    break

                current_page += 1

            except WMSAPIError as e:
                logger.error(f"Failed to retrieve page {current_page}: {e}")
                raise

        logger.info(f"Inventory retrieval complete: {len(all_inventory)} total items")
        return all_inventory

    def search_inventory_by_sku(self, sku_name: str) -> List[Dict[str, Any]]:
        """
        Search for inventory items by SKU pattern.
        Note: This is a client-side filter since the API doesn't seem to support server-side filtering.

        Args:
            sku_name: SKU name to search for (case-insensitive)

        Returns:
            List of matching inventory items
        """
        logger.info(f"Searching inventory for SKU pattern: {sku_name}")

        all_inventory = self.get_all_inventory(sku_name=sku_name)

        logger.info(f"Found {len(all_inventory)} items matching SKU pattern '{sku_name}'")
        return all_inventory

    def get_inventory_summary(self) -> Dict[str, Any]:
        """
        Get a summary of inventory without retrieving all data.
        
        Returns:
            Dictionary with inventory summary information
        """
        logger.info("Fetching inventory summary")

        # Get first page to extract pagination info
        first_page = self.get_inventory_page(page=1, limit=1)
        page_info = first_page['page_info']

        summary = {
            'total_pages': page_info['total_pages'],
            'current_page': page_info['current_page'],
            'estimated_total_items': page_info['total_pages'] * 10,  # Rough estimate
            'sample_item': first_page['data'][0] if first_page['data'] else None,
            'total_items': len(first_page['data'])
        }

        logger.info(f"Inventory summary: {page_info['total_pages']} pages available")
        return summary
