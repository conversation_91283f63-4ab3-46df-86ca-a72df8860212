import os
import logging
import httpx
from typing import List, Dict, Any
from django.conf import settings

logger = logging.getLogger(__name__)


class OMSClient:
    """Simple synchronous HTTP client to call OMS APIs for initiating returns."""

    def __init__(self):
        # Allow override via environment
        self.base_url = os.getenv('OMS_BASE_URL', '') or getattr(settings, 'OMS_BASE_URL', '')
        # Optionally use an API key or client credentials
        self.api_key = os.getenv('OMS_API_KEY', '')
        self.timeout = float(os.getenv('OMS_TIMEOUT', '30'))

    def _headers(self) -> Dict[str, str]:
        headers = {'Content-Type': 'application/json'}
        if self.api_key:
            headers['Authorization'] = f'Bearer {self.api_key}'
        return headers

    def initiate_partial_return(self, order_id: str, items: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Call OMS app endpoint to return specific items.

        Expects OMS to expose a return endpoint like /app/v1/return_items
        with body: {"order_id": ..., "items": [{"sku":..., "quantity":...}, ...]}
        Returns a dict with status_code and data.
        """
        if not self.base_url:
            raise RuntimeError('OMS_BASE_URL not configured')

        endpoint = f"{self.base_url.rstrip('/')}/app/v1/return_items"

        payload = {
            "order_id": order_id,
            "items": items
        }

        with httpx.Client(timeout=self.timeout) as client:
            logger.info(f"Calling OMS partial return endpoint: {endpoint}")
            resp = client.post(endpoint, json=payload, headers=self._headers())
            try:
                data = resp.json() if resp.content else {}
            except Exception:
                data = {"status_code": resp.status_code, "text": resp.text}

            return {"status_code": resp.status_code, "data": data}

    def initiate_full_return(self, order_id: str) -> Dict[str, Any]:
        if not self.base_url:
            raise RuntimeError('OMS_BASE_URL not configured')

        endpoint = f"{self.base_url.rstrip('/')}/app/v1/return_full_order"

        payload = {"order_id": order_id}

        with httpx.Client(timeout=self.timeout) as client:
            logger.info(f"Calling OMS full return endpoint: {endpoint}")
            resp = client.post(endpoint, json=payload, headers=self._headers())
            try:
                data = resp.json() if resp.content else {}
            except Exception:
                data = {"status_code": resp.status_code, "text": resp.text}

            return {"status_code": resp.status_code, "data": data}


oms_client = OMSClient()
