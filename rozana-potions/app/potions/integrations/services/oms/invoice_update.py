"""
OMS Invoice Update Service

This service handles updating invoice URLs in the OMS (Order Management System)
database. It provides methods to update individual orders or bulk update multiple
orders with their corresponding invoice PDF URLs.
"""

import logging
from django.db import connections
from django.db.utils import DatabaseError
from django.conf import settings
from typing import Dict, Any

logger = logging.getLogger(__name__)


class OMSInvoiceUpdateService:
    """
    Service class to handle OMS invoice URL updates with proper connection management.
    
    This service uses Django's multi-database support with connection pooling
    and automatic connection management for updating invoice URLs in the OMS orders table.
    """
    
    def __init__(self, database_alias: str = 'oms'):
        """
        Initialize the OMS database service.
        
        Args:
            database_alias: The database alias configured in Django settings
        """
        self.database_alias = database_alias
        self.connection = None
        
    def get_connection(self):
        """
        Get the database connection using Django's connection management.
        Returns:
            Database connection object
        Raises:
            DatabaseError: If connection cannot be established
        """
        try:
            if not self.connection:
                self.connection = connections[self.database_alias]
            return self.connection
        except Exception as e:
            logger.error(f"Failed to connect to OMS database '{self.database_alias}': {str(e)}")
            raise DatabaseError(f"OMS database connection failed: {str(e)}")

    def update_order_invoice_key_raw(self, order_id: str, invoice_key: str) -> Dict[str, Any]:
        """
        Update the invoice key using raw SQL for better performance or complex queries.
        
        Args:
            order_id (str): The order ID to update
            invoice_key (str): The S3 key of the generated invoice PDF
            
        Returns:
            dict: Update result with success status and details
        """
        try:
            # Validate inputs
            if not order_id or not invoice_key:
                return {
                    'success': False,
                    'error': 'Order ID and invoice key are required',
                    'order_id': order_id
                }
            
            connection = self.get_connection()
            
            with connection.cursor() as cursor:
                # Execute the update query
                update_query = """
                    UPDATE orders 
                    SET invoice_key = %s, updated_at = NOW()
                    WHERE order_id = %s
                """
                
                cursor.execute(update_query, [invoice_key, order_id])
                updated_count = cursor.rowcount
                
                if updated_count > 0:
                    logger.info(f"Successfully updated invoice key for order {order_id}: {invoice_key}")
                    return {
                        'success': True,
                        'order_id': order_id,
                        'invoice_key': invoice_key,
                        'updated_count': updated_count
                    }
                else:
                    logger.warning(f"No order found with ID {order_id} in OMS database")
                    return {
                        'success': False,
                        'error': f'Order not found with ID: {order_id}',
                        'order_id': order_id
                    }
        except DatabaseError as e:
            logger.error(f"Database error updating invoice key for order {order_id}: {str(e)}")
            return {
                'success': False,
                'error': f'Database error: {str(e)}',
                'order_id': order_id
            }
        except Exception as e:
            logger.error(f"Unexpected error updating invoice key for order {order_id}: {str(e)}")
            return {
                'success': False,
                'error': f'Unexpected error: {str(e)}',
                'order_id': order_id
            }

# Convenience function for quick access
def update_order_invoice_key(order_id: str, invoice_key: str) -> Dict[str, Any]:
    """
    Convenience function to update order invoice key.
    Args:
        order_id: The order ID to update
        invoice_key: The S3 key of the generated invoice PDF
    Returns:
        dict: Result dictionary with success status and details
    """
    service = OMSInvoiceUpdateService()
    return service.update_order_invoice_key_raw(order_id, invoice_key)
