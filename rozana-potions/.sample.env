# Potions Database Configuration
DATABASE_HOST=db1
DATABASE_PORT=5432
DATABASE_NAME=potions
DATABASE_USER=postgres
DATABASE_PASSWORD=rozana^1234

# OMS Database Configuration
OMS_DATABASE_HOST=host.docker.internal
OMS_DATABASE_PORT=5436
OMS_DATABASE_NAME=oms
OMS_DATABASE_USER=postgres
OMS_DATABASE_PASSWORD=rozana^1234

# Redis Configuration
REDIS_BROKER_URL=redis://redis:6379

# TMS Configuration
TMS_API_KEY=tms_key
TMS_WEBHOOK_TOKEN=your-tms-webhook-token

# Invoice Webhook Configuration
INVOICE_WEBHOOK_TOKEN=your-invoice-webhook-token

# AWS Configuration
AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_STORAGE_BUCKET_NAME=